# 足球分析系统 (Football Analysis System)

一个基于Python和Tkinter的足球比赛分析系统，提供赔率分析、区间分析、基本面分析等功能。

## 功能特性

### 🎯 核心功能
- **赔率分析**: 分析各大博彩公司的赔率数据
- **区间分析**: 基于赔率计算区间类型和规则值
- **基本面分析**: 综合分析比赛基本面信息
- **历史数据分析**: 历史赔率变化趋势分析

### 📊 分析模块
- **多公司赔率对比**: 支持多家博彩公司数据
- **实时赔率监控**: 初赔和即时赔率对比
- **区间映射**: 智能区间类型识别
- **数据可视化**: 图表展示分析结果

### 🔧 技术特性
- **模块化设计**: 清晰的代码结构和组件分离
- **数据库支持**: SQLite数据库存储
- **GUI界面**: 基于Tkinter的用户友好界面
- **日志系统**: 完整的日志记录和错误处理

## 系统要求

- Python 3.7+
- tkinter (通常随Python安装)
- sqlite3 (Python标准库)
- matplotlib (用于图表显示)
- requests (用于网络请求)

## 安装说明

1. **克隆仓库**
   ```bash
   git clone https://github.com/您的用户名/football_analysis_system.git
   cd football_analysis_system
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python app.py
   ```

## 项目结构

```
football_analysis_system/
├── app.py                          # 主程序入口
├── football_analysis_system/       # 核心模块
│   ├── analysis/                   # 分析模块
│   │   ├── odds_analyzer.py        # 赔率分析器
│   │   ├── interval_analyzer.py    # 区间分析器
│   │   └── fundamental_analyzer.py # 基本面分析器
│   ├── ui/                         # 用户界面
│   │   ├── tabs/                   # 标签页组件
│   │   │   ├── interval_analysis_tab.py      # 区间分析标签页
│   │   │   ├── fundamental_analysis_tab.py   # 基本面分析标签页
│   │   │   └── historical_odds_interval_tab.py # 历史区间分析标签页
│   │   └── components/             # UI组件
│   ├── db/                         # 数据库模块
│   └── scrapers/                   # 数据抓取模块
├── data/                           # 数据文件
├── logs/                           # 日志文件
└── tests/                          # 测试文件
```

## 使用说明

### 基本操作
1. **启动程序**: 运行 `python app.py`
2. **选择比赛**: 在比赛列表中选择要分析的比赛
3. **查看分析**: 切换不同标签页查看各种分析结果

### 功能说明
- **区间分析**: 显示各博彩公司的区间类型和规则值
- **基本面分析**: 提供比赛的基本面分析结果
- **历史分析**: 查看历史赔率变化趋势

## 开发说明

### 代码规范
- 使用Python PEP 8编码规范
- 模块化设计，职责分离
- 完整的错误处理和日志记录

### 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 更新日志

### v1.0.0 (2024-08-06)
- ✨ 新增区间分析功能中的基本面分析按钮
- 🔧 从历史区间分析中移除基本面分析按钮
- 🎨 优化UI组件结构和代码组织
- 📝 完善文档和注释

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至: [您的邮箱]

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
