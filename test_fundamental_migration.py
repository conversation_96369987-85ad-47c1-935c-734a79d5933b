#!/usr/bin/env python3
"""
测试基本面分析功能迁移
验证基本面分析按钮从历史区间分析迁移到区间分析标签页是否成功
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_components():
    """测试UI组件是否正确创建基本面分析按钮"""
    print("测试UI组件...")
    
    try:
        from football_analysis_system.ui.tabs.ui_components import UIBuilder
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试基本面分析按钮迁移")
        root.geometry("800x600")
        
        # 创建UIBuilder实例
        ui_builder = UIBuilder(root, "#F5F5F5", "#3366CC", ("Arial", 12, "bold"))
        
        # 测试回调函数
        def test_help_callback():
            print("帮助按钮被点击")
            
        def test_save_callback():
            print("保存按钮被点击")
            
        def test_interval_click_callback(event):
            print("区间表格被点击")
            
        def test_fundamental_callback():
            print("基本面分析按钮被点击 - 功能迁移成功！")
            tk.messagebox.showinfo("成功", "基本面分析按钮功能迁移成功！")
        
        # 创建区间分析UI控件（包含基本面分析按钮）
        interval_tree, save_button, status_label = ui_builder.create_interval_widgets(
            test_help_callback,
            test_save_callback,
            test_interval_click_callback,
            test_fundamental_callback  # 传递基本面分析回调函数
        )
        
        # 添加说明标签
        info_label = tk.Label(root, text="测试基本面分析按钮迁移\n点击'获取基本面分析'按钮验证功能", 
                             font=("Arial", 14), fg="blue")
        info_label.pack(pady=20)
        
        print("✓ UI组件创建成功，基本面分析按钮已添加到区间分析标签页")
        
        # 运行测试窗口
        root.mainloop()
        
    except Exception as e:
        print(f"✗ UI组件测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_interval_analysis_tab():
    """测试区间分析标签页是否包含基本面分析方法"""
    print("\n测试区间分析标签页...")
    
    try:
        from football_analysis_system.ui.tabs.interval_analysis_tab import IntervalAnalysisTab
        
        # 检查是否包含基本面分析方法
        required_methods = [
            '_show_fundamental_popup',
            '_display_results_in_text_widget',
            '_display_no_results_in_text_widget',
            '_get_current_time',
            '_refresh_popup_data',
            '_show_test_data_in_popup'
        ]
        
        for method_name in required_methods:
            if hasattr(IntervalAnalysisTab, method_name):
                print(f"✓ 方法 {method_name} 已成功迁移")
            else:
                print(f"✗ 方法 {method_name} 迁移失败")
                
        print("✓ 区间分析标签页基本面分析方法迁移完成")
        
    except Exception as e:
        print(f"✗ 区间分析标签页测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_historical_tab_cleanup():
    """测试历史区间分析标签页是否已移除基本面分析按钮"""
    print("\n测试历史区间分析标签页清理...")
    
    try:
        # 读取历史区间分析标签页文件内容
        with open('football_analysis_system/ui/tabs/historical_odds_interval_tab.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还包含基本面分析相关代码
        removed_elements = [
            'text="获取基本面分析"',
            'command=self._show_fundamental_popup',
            'command=self._refresh_fundamental_analysis',
            'def _show_fundamental_popup',
            'def _refresh_fundamental_analysis',
            'def _display_fundamental_results',
            'def _show_no_analysis_message',
            'def _show_error_message',
            'def _show_test_data'
        ]
        
        all_removed = True
        for element in removed_elements:
            if element in content:
                print(f"✗ 仍然包含: {element}")
                all_removed = False
            else:
                print(f"✓ 已移除: {element}")
        
        if all_removed:
            print("✓ 历史区间分析标签页基本面分析功能已完全移除")
        else:
            print("✗ 历史区间分析标签页清理不完整")
            
    except Exception as e:
        print(f"✗ 历史区间分析标签页清理测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试基本面分析功能迁移...")
    print("=" * 50)
    
    # 测试UI组件
    test_ui_components()
    
    # 测试区间分析标签页
    test_interval_analysis_tab()
    
    # 测试历史标签页清理
    test_historical_tab_cleanup()
    
    print("\n" + "=" * 50)
    print("基本面分析功能迁移测试完成")

if __name__ == "__main__":
    main()
