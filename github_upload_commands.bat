@echo off
echo ========================================
echo 足球分析系统 GitHub 上传脚本
echo ========================================
echo.

echo 步骤1: 检查Git是否已安装...
git --version
if %errorlevel% neq 0 (
    echo 错误: Git未安装，请先安装Git
    echo 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

echo.
echo 步骤2: 进入项目目录...
cd /d "i:\football_analysis_system 0428\football_analysis_system"

echo.
echo 步骤3: 初始化Git仓库...
git init

echo.
echo 步骤4: 配置Git用户信息...
echo 请输入您的GitHub邮箱:
set /p email=

git config --global user.name "denleey1"
git config --global user.email "%email%"

echo.
echo 步骤5: 添加所有文件到Git...
git add .

echo.
echo 步骤6: 创建初始提交...
git commit -m "Initial commit: Football Analysis System with fundamental analysis migration"

echo.
echo 步骤7: 添加远程仓库...
echo 设置远程仓库为: https://github.com/denleey1/football_analysis_system.git
git remote add origin https://github.com/denleey1/football_analysis_system.git

echo.
echo 步骤8: 推送到GitHub...
echo 注意: 系统可能会要求您输入GitHub用户名和密码/令牌
git branch -M main
git push -u origin main

echo.
echo ========================================
echo 上传完成！
echo 您的项目现在已托管在GitHub上
echo ========================================
pause
