import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sqlite3
from datetime import datetime, timedelta
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, FONT_SUBHEADER, DB_GUANGYISHILI
from football_analysis_system.core.path_resolver import PathResolver
import os

class MahuiLeagueTab(tk.Frame):
    """马会联赛查询标签页"""

    def __init__(self, parent, db_path=None):
        """
        初始化马会联赛查询标签页

        Args:
            parent: 父容器
            db_path: 数据库路径
        """
        super().__init__(parent, bg=COLOR_BG)

        # 数据库路径
        self.guangyi_db_path = DB_GUANGYISHILI
        self.football_db_path = PathResolver.get_database_path('football.db')
        
        # 记录数据库路径用于调试
        logging.info(f"马会联赛查询标签页使用广义实力数据库: {self.guangyi_db_path}")
        logging.info(f"马会联赛查询标签页使用足球数据库: {self.football_db_path}")
        
        # 检查数据库文件是否存在
        if not os.path.exists(self.guangyi_db_path):
            logging.error(f"广义实力数据库文件不存在: {self.guangyi_db_path}")
        
        if not os.path.exists(self.football_db_path):
            logging.error(f"足球数据库文件不存在: {self.football_db_path}")
            
        self.mahui_leagues = []  # 包含马会的联赛列表
        self.upcoming_matches = []  # 未来7天的比赛
        
        self.create_widgets()
        self.load_mahui_leagues()

    def create_widgets(self):
        """创建标签页控件"""
        # 容器框架
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 顶部标题和控制区域
        top_frame = ttk.Frame(main_container)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        # 标题
        title_label = ttk.Label(top_frame, text="马会联赛查询",
                             font=FONT_SUBHEADER,
                             foreground=COLOR_PRIMARY,
                             background=COLOR_BG,
                             style="Subheader.TLabel")
        title_label.pack(side=tk.LEFT, padx=5)

        # 刷新按钮
        refresh_button = ttk.Button(top_frame, text="刷新", width=8,
                                  command=self.refresh_data,
                                  style="Primary.TButton")
        refresh_button.pack(side=tk.RIGHT, padx=5)

        # 查询按钮
        query_button = ttk.Button(top_frame, text="查询比赛", width=10,
                                command=self.query_upcoming_matches,
                                style="Primary.TButton")
        query_button.pack(side=tk.RIGHT, padx=5)

        # 查看数据按钮
        check_data_button = ttk.Button(top_frame, text="查看数据", width=10,
                                     command=self.check_database_data,
                                     style="Primary.TButton")
        check_data_button.pack(side=tk.RIGHT, padx=5)

        # 联赛列表区域
        leagues_frame = ttk.LabelFrame(main_container, text="包含马会的联赛")
        leagues_frame.pack(fill=tk.X, padx=5, pady=5)

        # 联赛操作按钮区域
        league_buttons_frame = ttk.Frame(leagues_frame)
        league_buttons_frame.pack(fill=tk.X, padx=5, pady=2)

        ttk.Button(league_buttons_frame, text="全选", width=8,
                  command=self.select_all_leagues).pack(side=tk.LEFT, padx=2)
        ttk.Button(league_buttons_frame, text="取消全选", width=8,
                  command=self.deselect_all_leagues).pack(side=tk.LEFT, padx=2)
        ttk.Button(league_buttons_frame, text="反选", width=8,
                  command=self.invert_league_selection).pack(side=tk.LEFT, padx=2)

        # 创建联赛列表
        self.leagues_listbox = tk.Listbox(leagues_frame, height=6, selectmode=tk.MULTIPLE)
        self.leagues_listbox.pack(fill=tk.X, padx=5, pady=5)

        # 比赛列表区域
        matches_frame = ttk.LabelFrame(main_container, text="未来7天比赛")
        matches_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview用于显示比赛
        columns = ("league", "home_team", "away_team", "match_time")
        self.matches_tree = ttk.Treeview(matches_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.matches_tree.heading("league", text="联赛")
        self.matches_tree.heading("home_team", text="主队")
        self.matches_tree.heading("away_team", text="客队")
        self.matches_tree.heading("match_time", text="比赛时间")
        
        # 设置列宽
        self.matches_tree.column("league", width=150)
        self.matches_tree.column("home_team", width=150)
        self.matches_tree.column("away_team", width=150)
        self.matches_tree.column("match_time", width=200)
        
        # 添加滚动条
        matches_scrollbar = ttk.Scrollbar(matches_frame, orient=tk.VERTICAL, command=self.matches_tree.yview)
        self.matches_tree.configure(yscrollcommand=matches_scrollbar.set)
        
        # 布局
        self.matches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        matches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_container, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5)

    def load_mahui_leagues(self):
        """从广义实力表中加载包含马会的联赛"""
        try:
            if not os.path.exists(self.guangyi_db_path):
                self.status_var.set("广义实力数据库不存在")
                return

            conn = sqlite3.connect(self.guangyi_db_path)
            cursor = conn.cursor()

            # 先查看数据库中有哪些公司
            cursor.execute("SELECT DISTINCT company_id, company_name FROM team_power_ratings")
            companies = cursor.fetchall()
            logging.info(f"数据库中的公司: {companies}")

            # 查询包含马会(香港马会)的联赛 - 使用company_id查询更准确
            cursor.execute('''
            SELECT DISTINCT league_name
            FROM team_power_ratings
            WHERE company_id = '432' OR company_name LIKE '%马会%' OR company_name LIKE '%香港马会%'
            ORDER BY league_name
            ''')

            leagues = cursor.fetchall()
            self.mahui_leagues = [league[0] for league in leagues if league[0]]
            logging.info(f"找到马会联赛: {self.mahui_leagues}")

            conn.close()
            
            # 更新联赛列表框
            self.leagues_listbox.delete(0, tk.END)
            for league in self.mahui_leagues:
                self.leagues_listbox.insert(tk.END, league)

            # 更新状态栏
            self.status_var.set(f"找到 {len(self.mahui_leagues)} 个包含马会的联赛")

            # 如果有联赛，默认选择前几个热门联赛
            if self.mahui_leagues:
                popular_leagues = ['英超', '西甲', '德甲', '意甲', '法甲', '日职联']
                for i, league in enumerate(self.mahui_leagues):
                    if league in popular_leagues:
                        self.leagues_listbox.select_set(i)
            
        except Exception as e:
            logging.error(f"加载马会联赛时出错: {e}")
            messagebox.showerror("错误", f"加载马会联赛失败: {e}")
            self.status_var.set("加载马会联赛失败")

    def query_upcoming_matches(self):
        """查询选中联赛未来7天的比赛"""
        try:
            # 获取选中的联赛
            selected_indices = self.leagues_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("提示", "请至少选择一个联赛")
                return
                
            selected_leagues = [self.mahui_leagues[i] for i in selected_indices]
            
            if not os.path.exists(self.football_db_path):
                self.status_var.set("足球数据库不存在")
                return

            # 计算未来7天的时间范围
            now = datetime.now()
            future_date = now + timedelta(days=7)
            
            # 格式化时间字符串
            now_str = now.strftime('%Y-%m-%d %H:%M:%S')
            future_str = future_date.strftime('%Y-%m-%d %H:%M:%S')
            
            conn = sqlite3.connect(self.football_db_path)
            cursor = conn.cursor()

            # 先查看表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            logging.info(f"football.db中的表: {[t[0] for t in tables]}")

            if 'matches' in [t[0] for t in tables]:
                cursor.execute("PRAGMA table_info(matches)")
                columns = cursor.fetchall()
                logging.info(f"matches表的列: {[c[1] for c in columns]}")

            # 清空现有比赛列表
            for item in self.matches_tree.get_children():
                self.matches_tree.delete(item)

            all_matches = []

            # 为每个选中的联赛查询比赛
            for league in selected_leagues:
                cursor.execute('''
                SELECT league_name, home_team_name, away_team_name, match_time
                FROM matches
                WHERE league_name = ?
                AND match_time >= ?
                AND match_time <= ?
                AND match_status = 0
                ORDER BY match_time
                ''', (league, now_str, future_str))

                matches = cursor.fetchall()
                logging.info(f"联赛 {league} 找到 {len(matches)} 场比赛")
                all_matches.extend(matches)

            conn.close()
            
            if all_matches:
                # 按联赛分组显示比赛
                current_league = None
                for match in sorted(all_matches, key=lambda x: (x[0], x[3])):  # 按联赛和时间排序
                    league_name, home_team, away_team, match_time = match

                    # 如果是新联赛，插入分隔行
                    if current_league != league_name:
                        current_league = league_name
                        # 插入联赛标题行
                        self.matches_tree.insert("", tk.END, values=(f"=== {league_name} ===", "", "", ""), tags=("league_header",))

                    # 插入比赛行
                    self.matches_tree.insert("", tk.END, values=(league_name, home_team, away_team, match_time))

                # 配置联赛标题行的样式
                self.matches_tree.tag_configure("league_header", background="#e3f2fd", font=("Arial", 10, "bold"))

                # 更新状态栏
                self.status_var.set(f"找到 {len(all_matches)} 场未来7天的比赛")
            else:
                # 没有找到比赛，显示提示信息
                self.matches_tree.insert("", tk.END, values=("暂无数据", "未来7天内没有找到比赛", "可能是数据库中没有未来比赛数据", ""), tags=("no_data",))
                self.matches_tree.tag_configure("no_data", background="#fff3e0", foreground="#e65100")
                self.status_var.set(f"未找到未来7天的比赛，可能数据库中只有历史数据")
            
        except Exception as e:
            logging.error(f"查询未来比赛时出错: {e}")
            messagebox.showerror("错误", f"查询未来比赛失败: {e}")
            self.status_var.set("查询未来比赛失败")

    def check_database_data(self):
        """查看数据库中的数据情况"""
        try:
            if not os.path.exists(self.football_db_path):
                messagebox.showwarning("提示", "足球数据库不存在")
                return

            conn = sqlite3.connect(self.football_db_path)
            cursor = conn.cursor()

            # 获取选中的联赛
            selected_indices = self.leagues_listbox.curselection()
            if not selected_indices:
                # 如果没有选中联赛，显示所有联赛的数据概况
                cursor.execute("SELECT league_name, COUNT(*) as match_count FROM matches GROUP BY league_name ORDER BY match_count DESC LIMIT 10")
                data = cursor.fetchall()

                info_text = "数据库中比赛数量最多的前10个联赛：\n\n"
                for league, count in data:
                    info_text += f"{league}: {count} 场比赛\n"

                # 查看时间范围
                cursor.execute("SELECT MIN(match_time), MAX(match_time) FROM matches")
                time_range = cursor.fetchone()
                if time_range[0] and time_range[1]:
                    info_text += f"\n时间范围: {time_range[0]} 至 {time_range[1]}"

            else:
                # 显示选中联赛的详细信息
                selected_leagues = [self.mahui_leagues[i] for i in selected_indices]
                info_text = "选中联赛的数据情况：\n\n"

                for league in selected_leagues:
                    cursor.execute("SELECT COUNT(*) FROM matches WHERE league_name = ?", (league,))
                    total_count = cursor.fetchone()[0]

                    cursor.execute("SELECT MIN(match_time), MAX(match_time) FROM matches WHERE league_name = ?", (league,))
                    time_range = cursor.fetchone()

                    info_text += f"{league}:\n"
                    info_text += f"  总比赛数: {total_count}\n"
                    if time_range[0] and time_range[1]:
                        info_text += f"  时间范围: {time_range[0]} 至 {time_range[1]}\n"
                    info_text += "\n"

            conn.close()
            messagebox.showinfo("数据库数据情况", info_text)

        except Exception as e:
            logging.error(f"查看数据库数据时出错: {e}")
            messagebox.showerror("错误", f"查看数据库数据失败: {e}")

    def select_all_leagues(self):
        """全选联赛"""
        self.leagues_listbox.select_set(0, tk.END)

    def deselect_all_leagues(self):
        """取消全选联赛"""
        self.leagues_listbox.selection_clear(0, tk.END)

    def invert_league_selection(self):
        """反选联赛"""
        current_selection = set(self.leagues_listbox.curselection())
        all_indices = set(range(self.leagues_listbox.size()))
        new_selection = all_indices - current_selection

        self.leagues_listbox.selection_clear(0, tk.END)
        for index in new_selection:
            self.leagues_listbox.select_set(index)

    def refresh_data(self):
        """刷新数据"""
        self.load_mahui_leagues()
        # 清空比赛列表
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)
        self.status_var.set("数据已刷新")
