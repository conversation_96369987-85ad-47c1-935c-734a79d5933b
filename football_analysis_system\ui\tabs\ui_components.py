import tkinter as tk
from tkinter import ttk, scrolledtext
import logging

class UIBuilder:
    """UI组件构建器，负责创建和管理UI元素"""
    
    def __init__(self, parent, color_bg, color_primary, font_subheader):
        """
        初始化UI构建器
        
        Args:
            parent: 父容器
            color_bg: 背景颜色
            color_primary: 主要颜色
            font_subheader: 子标题字体
        """
        self.parent = parent
        self.color_bg = color_bg
        self.color_primary = color_primary
        self.font_subheader = font_subheader
        
        # 设置父容器背景色
        if parent:
            parent.configure(bg=color_bg)
        
        # UI元素引用
        self.interval_tree = None
        self.save_button = None
        self.interval_frame = None
        self.fundamental_frame = None
        self.fundamental_text = None
        self.analyze_button = None
        self.stop_button = None
        self.status_label = None
        self.progress_bar = None
        self.progress_var = None
        self.help_window = None  # 添加问号窗口引用，用于跟踪窗口状态
        
    def create_widgets(self, help_callback, save_callback, interval_click_callback, fundamental_analysis_callback, stop_analysis_callback=None, fundamental_help_callback=None):
        """
        创建标签页控件（包含区间分析和基本面分析）
        
        Args:
            help_callback: 帮助按钮回调函数
            save_callback: 保存按钮回调函数
            interval_click_callback: 区间表格点击回调函数
            fundamental_analysis_callback: 基本面分析按钮回调函数
            stop_analysis_callback: 停止分析按钮回调函数
            fundamental_help_callback: 基本面分析帮助按钮回调函数
            
        Returns:
            tuple: (interval_tree, fundamental_text, save_button, analyze_button, stop_button, status_label, progress_bar)
        """
        # 容器框架 - 使用更现代的布局
        interval_container = ttk.Frame(self.parent)
        interval_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 区间分析表格标题框架（包含标题和问号按钮）
        interval_title_frame = ttk.Frame(interval_container)
        interval_title_frame.pack(fill=tk.X, padx=5, pady=5)

        # 区间分析结果标题 - 使用更现代的样式
        interval_title_label = ttk.Label(interval_title_frame, text="区间分析结果",
                                     font=self.font_subheader,
                                     foreground=self.color_primary,
                                     background=self.color_bg,
                                     style="Subheader.TLabel")
        interval_title_label.pack(side=tk.LEFT, padx=5)

        # 添加问号按钮 - 使用更现代的按钮样式
        help_button = ttk.Button(interval_title_frame, text="?", width=2,
                              command=help_callback,
                              style="Primary.TButton")
        help_button.pack(side=tk.LEFT, padx=5)

        # 添加保存按钮
        self.save_button = ttk.Button(interval_title_frame, text="保存预测", width=10,
                                  command=save_callback,
                                  style="Primary.TButton")
        self.save_button.pack(side=tk.RIGHT, padx=5)

        # 区间分析表格框架 - 使用现代化的卡片式设计
        self.interval_frame = ttk.LabelFrame(interval_container, text="",
                                          style="TLabelframe")
        self.interval_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview用于显示区间分析
        columns = ("select", "odds_type", "company", "team_type", "gap_diff", "interval_type", "rule_value", "draw_interval", "choice")
        self.interval_tree = ttk.Treeview(self.interval_frame, columns=columns, show="headings", height=20)

        # 设置行高为18像素（默认为20）
        style = ttk.Style()
        style.configure("Treeview", rowheight=18)

        # 设置列宽
        self.interval_tree.column("select", width=40, anchor=tk.CENTER)
        self.interval_tree.column("odds_type", width=80)
        self.interval_tree.column("company", width=100)
        self.interval_tree.column("team_type", width=50, anchor=tk.CENTER)
        self.interval_tree.column("gap_diff", width=70, anchor=tk.CENTER)
        self.interval_tree.column("interval_type", width=150, anchor=tk.CENTER)
        self.interval_tree.column("rule_value", width=70, anchor=tk.CENTER)
        self.interval_tree.column("draw_interval", width=100, anchor=tk.CENTER)
        self.interval_tree.column("choice", width=80, anchor=tk.CENTER)

        # 设置列标题
        self.interval_tree.heading("select", text="选择")
        self.interval_tree.heading("odds_type", text="赔率类型")
        self.interval_tree.heading("company", text="投注公司")
        self.interval_tree.heading("team_type", text="队伍")
        self.interval_tree.heading("gap_diff", text="档距差")
        self.interval_tree.heading("interval_type", text="盘口类型")
        self.interval_tree.heading("rule_value", text="规则值")
        self.interval_tree.heading("draw_interval", text="平赔区间")
        self.interval_tree.heading("choice", text="首选")

        # 滚动条
        interval_scroll_y = ttk.Scrollbar(self.interval_frame, orient="vertical", command=self.interval_tree.yview)
        interval_scroll_x = ttk.Scrollbar(self.interval_frame, orient="horizontal", command=self.interval_tree.xview)
        self.interval_tree.configure(yscrollcommand=interval_scroll_y.set, xscrollcommand=interval_scroll_x.set)

        # 布局
        self.interval_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        interval_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        interval_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 添加基本面分析框架 - 替换原来的平赔区间分析
        self.fundamental_frame = ttk.LabelFrame(interval_container, text="基本面分析",
                                              style="TLabelframe")
        self.fundamental_frame.pack(fill=tk.BOTH, padx=5, pady=(15, 5))  # 调整上下间距，上间距15，下间距5

        # 创建控制面板框架
        control_frame = tk.Frame(self.fundamental_frame, bg=self.color_bg)
        control_frame.pack(fill=tk.X, padx=10, pady=5)

        # 添加分析按钮
        self.analyze_button = ttk.Button(control_frame, text="开始基本面分析",
                                       style='Primary.TButton',
                                       command=fundamental_analysis_callback)
        self.analyze_button.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 添加停止按钮
        self.stop_button = ttk.Button(control_frame, text="停止分析",
                                   style='Accent.TButton', 
                                   state=tk.DISABLED)
        if stop_analysis_callback:
            self.stop_button.config(command=stop_analysis_callback)
        self.stop_button.pack(side=tk.LEFT, padx=5, pady=5)
            
        # 添加帮助按钮（问号按钮）
        self.help_button = ttk.Button(control_frame, text="?", width=2,
                                     style="Primary.TButton")
        if fundamental_help_callback:
            self.help_button.config(command=fundamental_help_callback)
        self.help_button.pack(side=tk.LEFT, padx=5, pady=5)

        # 添加状态标签
        self.status_label = tk.Label(control_frame, text="",
                                   font=("Helvetica", 10, "italic"),
                                   fg="#666666", bg=self.color_bg)
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

        # 添加进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame,
                                          style="TProgressbar",
                                          orient="horizontal",
                                          length=300,
                                          mode="indeterminate",
                                          variable=self.progress_var)
        self.progress_bar.pack(side=tk.LEFT, padx=10, pady=5)
        self.progress_bar.pack_forget()  # 初始隐藏

        # 添加基本面分析结果文本框
        self.fundamental_text = scrolledtext.ScrolledText(self.fundamental_frame,
                                                   wrap=tk.WORD,
                                                   width=80,
                                                   height=10,
                                                   font=("Consolas", 10),
                                                   bg=self.color_bg,
                                                   fg=self.color_primary)
        self.fundamental_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.fundamental_text.config(state=tk.DISABLED)  # 只读模式

        # 应用特殊样式 - 使用更细的分隔线
        self.interval_tree.tag_configure('separator', background='#f0f0f0')  # 更浅的分隔线颜色

        # 为规则值不同的范围配置不同的颜色标签
        self.interval_tree.tag_configure('good', background='#90EE90')  # 浅绿色，规则值 ≤ 3
        self.interval_tree.tag_configure('normal', background='#FFFF99') # 浅黄色，规则值 3-4.5
        self.interval_tree.tag_configure('bad', background='#FFA07A')   # 浅橙色，规则值 4.5-6
        self.interval_tree.tag_configure('very_bad', background='#FF6B6B') # 浅红色，规则值 > 6

        # 为主队和客队单独创建标签
        self.interval_tree.tag_configure('home_good', background='#90EE90')
        self.interval_tree.tag_configure('home_normal', background='#FFFF99')
        self.interval_tree.tag_configure('home_bad', background='#FFA07A')
        self.interval_tree.tag_configure('home_very_bad', background='#FF6B6B')

        self.interval_tree.tag_configure('away_good', background='#90EE90')
        self.interval_tree.tag_configure('away_normal', background='#FFFF99')
        self.interval_tree.tag_configure('away_bad', background='#FFA07A')
        self.interval_tree.tag_configure('away_very_bad', background='#FF6B6B')

        # 添加超高水/超低水提示标签 - 保持背景色不变，仅加粗文本
        self.interval_tree.tag_configure('extreme_water', font=('Arial', 10, 'bold'))

        # 添加分组标题样式
        self.interval_tree.tag_configure('group_title', background='#E0E0E0', font=('Arial', 10, 'bold'))
        
        # 绑定点击事件处理选择/取消选择
        self.interval_tree.bind("<ButtonRelease-1>", interval_click_callback)
        
        return (self.interval_tree, self.fundamental_text, self.save_button, 
                self.analyze_button, self.stop_button, self.status_label, self.progress_bar)
        
    def show_odds_interval_help(self):
        """显示赔率区间分析帮助信息"""
        # 检查是否已有帮助窗口打开
        if self.help_window is not None and self.help_window.winfo_exists():
            # 如果已经存在窗口，将其提到前台
            self.help_window.lift()
            self.help_window.focus_set()
            return
            
        help_text = """赔率分析区间？

原则上赛果更倾向于规则数越小的一方。
以马会或者澳门的初盘为基准方向，欧洲盘的即时盘为赛果的确认方向。
初盘是倾向，变盘是对初盘的否认或者认可。临场盘，当阵容出现的那一刻，所有的变盘行为指向的都是赛果。

规则值颜色说明：
绿色表示：完全认可。
黄色表示：不完全认可。
粉色表示：否认掉99%，余下的1%的可能性需要后期变盘且另外两个方向的配合分散。
红色表示：完全否认，没有考虑的必要。

主客双方都是绿色（3.0以内）：表明主客双方拥有同等击败对手的机会，因此，胜平负皆有可能。后期若是变盘否认掉哪方，就代表不看好。一旦开到此盘，99%就表示强队没有取胜机会，这种情况基本平局首选。
如果强队状态差，那么本场强队胜出概率大。如果强队状态好，那么出平的概率很大。

主客双方都是黄色（3.5，4.0）：表明主客双方均不具备确切击败对手的能力。因此，平赔是最佳方向。
此种盘口并没有否认掉强队取胜机会，后期3.5变盘为绿色代表正向看好。
4.0变盘为绿色（2.5），通常代表着诱。
4.0变盘为绿色（2.5以下），通常代表着阻。

主客双方都是粉色（4.5）：表明主客双方都不具备取胜能力。因此，平赔是唯一方向。

超实盘超高水状态很好，必然打出。状态很差，实开盘超低水，必然打出

推荐当中的赛果，表明的是强队的最大限度能力。
主队具备主场优势，因此即便保主队落于客队1.5档，依然属于强队。

平局表示：弱队至少能逼平。

双平偏平表示：强队的取胜能力不足，弱队大概率能绝平。

双平偏让平表明：强队的取胜能力是可以的，具备临场绝杀能力。

让1平：表明这个队具备稳定赢1球的能力。

让1胜：表明这个队具备稳定赢1球的能力，甚至两球。

让1负：表明这个队连保平的能力都欠缺。

--------------------------------

平赔区间说明：

平赔区间是根据标准平赔计算出的理论区间范围，用于判断实际平赔的偏离程度。不同区间代表不同的市场态度：

浅绿色区间（低开平、平实低）：平赔略低于标准值，通常认为平局可能性偏低，主客胜的机会较大。

浅黄色区间（平中庸下、平中庸上）：平赔接近标准值，市场认为平局可能性与理论预期相符。

浅橙色区间（高开平、平实高）：平赔高于标准值，市场对平局可能性评估较高。

浅红色区间（韬光平、超韬平、超深平、超散平、超实平）：平赔严重偏离标准值，市场对比赛结果有强烈偏好，需谨慎判断。

标准平赔是根据主胜和客胜赔率理论计算得出的，实际平赔的偏离程度反映了市场对平局可能性的看法。"""

        # 创建一个新的窗口
        self.help_window = tk.Toplevel(self.parent)
        self.help_window.title("赔率区间分析说明")
        self.help_window.geometry("800x600")
        self.help_window.configure(bg=self.color_bg)
        
        # 设置窗口关闭时的回调函数，清除窗口引用
        self.help_window.protocol("WM_DELETE_WINDOW", self._on_help_window_close)

        # 添加文本框显示帮助文本
        text_widget = tk.Text(self.help_window, wrap="word", bg=self.color_bg, fg=self.color_primary)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 插入帮助文本
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)  # 设为只读

        # 添加滚动条
        scrollbar = ttk.Scrollbar(text_widget, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(yscrollcommand=scrollbar.set)

        # 确保窗口在前台显示
        self.help_window.transient(self.parent)
        self.help_window.grab_set()

        # 添加关闭按钮
        close_button = tk.Button(self.help_window, text="关闭", command=self._on_help_window_close)
        close_button.pack(pady=10)
        
    def _on_help_window_close(self):
        """处理帮助窗口关闭事件"""
        if self.help_window:
            self.help_window.destroy()
            self.help_window = None
    
    # 添加基本面分析相关控制方法
    def show_status(self, text):
        """
        显示状态信息
        
        Args:
            text: 状态文本
        """
        if self.status_label:
            self.status_label.config(text=text)
    
    def start_progress(self):
        """开始进度条动画"""
        if self.progress_bar:
            self.progress_bar.pack(side=tk.LEFT, padx=10, pady=5)
            self.progress_bar.start(10)
    
    def stop_progress(self):
        """停止进度条动画"""
        if self.progress_bar:
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def enable_analyze_button(self):
        """启用分析按钮"""
        if self.analyze_button:
            self.analyze_button.config(state=tk.NORMAL)
    
    def disable_analyze_button(self):
        """禁用分析按钮"""
        if self.analyze_button:
            self.analyze_button.config(state=tk.DISABLED)
    
    def enable_stop_button(self):
        """启用停止按钮"""
        if self.stop_button:
            self.stop_button.config(state=tk.NORMAL)
        
    def disable_stop_button(self):
        """禁用停止按钮"""
        if self.stop_button:
            self.stop_button.config(state=tk.DISABLED)
    
    def append_fundamental_result(self, text):
        """
        向基本面分析结果文本框追加内容
        
        Args:
            text: 要追加的文本
        """
        if self.fundamental_text:
            self.fundamental_text.config(state=tk.NORMAL)
            self.fundamental_text.insert(tk.END, text)
            self.fundamental_text.see(tk.END)
            self.fundamental_text.config(state=tk.DISABLED)
    
    def append_colored_fundamental_result(self, text, color=None, tag=None):
        """
        向基本面分析结果文本框追加带颜色的内容
        
        Args:
            text: 要追加的文本
            color: 文本颜色
            tag: 标签名称（如果为None则自动生成）
        """
        if self.fundamental_text:
            self.fundamental_text.config(state=tk.NORMAL)

            if tag is None and color is not None:
                # 为每次插入的不同颜色文本创建唯一标签
                tag = f"color_{len(self.fundamental_text.tag_names())}"
                self.fundamental_text.tag_configure(tag, foreground=color)

            if tag:
                self.fundamental_text.insert(tk.END, text, tag)
            else:
                self.fundamental_text.insert(tk.END, text)

            self.fundamental_text.see(tk.END)
            self.fundamental_text.config(state=tk.DISABLED)
    
    def clear_fundamental_result(self):
        """清空基本面分析结果"""
        if self.fundamental_text:
            self.fundamental_text.config(state=tk.NORMAL)
            self.fundamental_text.delete(1.0, tk.END)
            self.fundamental_text.config(state=tk.DISABLED)

    def create_interval_widgets(self, help_callback, save_callback, interval_click_callback, fundamental_callback=None):
        """
        创建区间分析相关的UI控件（现在包含基本面分析按钮）

        Args:
            help_callback: 帮助按钮回调函数
            save_callback: 保存按钮回调函数
            interval_click_callback: 区间表格点击回调函数
            fundamental_callback: 基本面分析按钮回调函数

        Returns:
            tuple: (interval_tree, save_button, status_label)
        """
        # 容器框架 - 使用更现代的布局
        interval_container = ttk.Frame(self.parent)
        interval_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 区间分析表格标题框架（包含标题和问号按钮）
        interval_title_frame = ttk.Frame(interval_container)
        interval_title_frame.pack(fill=tk.X, padx=5, pady=5)

        # 区间分析结果标题 - 使用更现代的样式
        interval_title_label = ttk.Label(interval_title_frame, text="区间分析结果",
                                     font=self.font_subheader,
                                     foreground=self.color_primary,
                                     background=self.color_bg,
                                     style="Subheader.TLabel")
        interval_title_label.pack(side=tk.LEFT, padx=5)

        # 添加问号按钮 - 使用更现代的按钮样式
        help_button = ttk.Button(interval_title_frame, text="?", width=2,
                              command=help_callback,
                              style="Primary.TButton")
        help_button.pack(side=tk.LEFT, padx=5)

        # 添加基本面分析按钮（如果提供了回调函数）
        if fundamental_callback:
            fundamental_button = tk.Button(interval_title_frame, text="获取基本面分析",
                                         command=fundamental_callback,
                                         bg="#E91E63", fg="white",
                                         font=("Arial", 9, "bold"),
                                         relief=tk.FLAT, padx=20)
            fundamental_button.pack(side=tk.LEFT, padx=(10, 0))

        # 添加保存按钮
        self.save_button = ttk.Button(interval_title_frame, text="保存预测", width=10,
                                  command=save_callback,
                                  style="Primary.TButton")
        self.save_button.pack(side=tk.RIGHT, padx=5)
        
        # 添加状态标签
        self.status_label = tk.Label(interval_title_frame, text="",
                                   font=("Helvetica", 10, "italic"),
                                   fg="#666666", bg=self.color_bg)
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=5)

        # 区间分析表格框架 - 使用现代化的卡片式设计
        self.interval_frame = ttk.LabelFrame(interval_container, text="",
                                          style="TLabelframe")
        self.interval_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview用于显示区间分析
        columns = ("select", "odds_type", "company", "team_type", "gap_diff", "interval_type", "rule_value", "draw_interval", "choice")
        self.interval_tree = ttk.Treeview(self.interval_frame, columns=columns, show="headings", height=20)

        # 设置行高为18像素（默认为20）
        style = ttk.Style()
        style.configure("Treeview", rowheight=18)

        # 设置列宽
        self.interval_tree.column("select", width=40, anchor=tk.CENTER)
        self.interval_tree.column("odds_type", width=80)
        self.interval_tree.column("company", width=100)
        self.interval_tree.column("team_type", width=50, anchor=tk.CENTER)
        self.interval_tree.column("gap_diff", width=70, anchor=tk.CENTER)
        self.interval_tree.column("interval_type", width=150, anchor=tk.CENTER)
        self.interval_tree.column("rule_value", width=70, anchor=tk.CENTER)
        self.interval_tree.column("draw_interval", width=100, anchor=tk.CENTER)
        self.interval_tree.column("choice", width=80, anchor=tk.CENTER)

        # 设置列标题
        self.interval_tree.heading("select", text="选择")
        self.interval_tree.heading("odds_type", text="赔率类型")
        self.interval_tree.heading("company", text="投注公司")
        self.interval_tree.heading("team_type", text="队伍")
        self.interval_tree.heading("gap_diff", text="档距差")
        self.interval_tree.heading("interval_type", text="盘口类型")
        self.interval_tree.heading("rule_value", text="规则值")
        self.interval_tree.heading("draw_interval", text="平赔区间")
        self.interval_tree.heading("choice", text="首选")

        # 滚动条
        interval_scroll_y = ttk.Scrollbar(self.interval_frame, orient="vertical", command=self.interval_tree.yview)
        interval_scroll_x = ttk.Scrollbar(self.interval_frame, orient="horizontal", command=self.interval_tree.xview)
        self.interval_tree.configure(yscrollcommand=interval_scroll_y.set, xscrollcommand=interval_scroll_x.set)

        # 布局
        self.interval_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        interval_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        interval_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 应用特殊样式 - 使用更细的分隔线
        self.interval_tree.tag_configure('separator', background='#f0f0f0')  # 更浅的分隔线颜色

        # 为规则值不同的范围配置不同的颜色标签
        self.interval_tree.tag_configure('good', background='#90EE90')  # 浅绿色，规则值 ≤ 3
        self.interval_tree.tag_configure('normal', background='#FFFF99') # 浅黄色，规则值 3-4.5
        self.interval_tree.tag_configure('bad', background='#FFA07A')   # 浅橙色，规则值 4.5-6
        self.interval_tree.tag_configure('very_bad', background='#FF6B6B') # 浅红色，规则值 > 6

        # 为主队和客队单独创建标签
        self.interval_tree.tag_configure('home_good', background='#90EE90')
        self.interval_tree.tag_configure('home_normal', background='#FFFF99')
        self.interval_tree.tag_configure('home_bad', background='#FFA07A')
        self.interval_tree.tag_configure('home_very_bad', background='#FF6B6B')

        self.interval_tree.tag_configure('away_good', background='#90EE90')
        self.interval_tree.tag_configure('away_normal', background='#FFFF99')
        self.interval_tree.tag_configure('away_bad', background='#FFA07A')
        self.interval_tree.tag_configure('away_very_bad', background='#FF6B6B')

        # 添加超高水/超低水提示标签 - 保持背景色不变，仅加粗文本
        self.interval_tree.tag_configure('extreme_water', font=('Arial', 10, 'bold'))

        # 添加分组标题样式
        self.interval_tree.tag_configure('group_title', background='#E0E0E0', font=('Arial', 10, 'bold'))

        # 添加平赔区间样式
        self.interval_tree.tag_configure('draw_low', background='#C8E6C9')    # 浅绿色 - 低开平、平实低
        self.interval_tree.tag_configure('draw_normal', background='#FFF9C4')  # 浅黄色 - 平中庸
        self.interval_tree.tag_configure('draw_high', background='#FFCCBC')    # 浅橙色 - 高开平、平实高
        self.interval_tree.tag_configure('draw_extreme', background='#FFCDD2') # 浅红色 - 韬光平、超韬平、超深平、超散平、超实平

        # 绑定点击事件
        self.interval_tree.bind("<ButtonRelease-1>", interval_click_callback)

        # 返回所有创建的组件
        return self.interval_tree, self.save_button, self.status_label